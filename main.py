import math
from typing import Dict, Any


def calculator_tool(expression: str) -> str:
    """计算数学表达式的工具"""
    try:
        # 安全的数学计算，只允许基本的数学操作
        allowed_names = {
            k: v for k, v in math.__dict__.items() if not k.startswith("__")
        }
        allowed_names.update({"abs": abs, "round": round})

        result = eval(expression, {"__builtins__": {}}, allowed_names)
        return f"计算结果: {result}"
    except Exception as e:
        return f"计算错误: {str(e)}"


def weather_tool(city: str) -> str:
    """获取天气信息的模拟工具"""
    # 这是一个模拟的天气工具，实际使用时可以接入真实的天气API
    weather_data = {
        "北京": "晴天，温度 15°C",
        "上海": "多云，温度 18°C",
        "广州": "小雨，温度 22°C",
        "深圳": "晴天，温度 25°C"
    }

    return weather_data.get(city, f"抱歉，暂时无法获取{city}的天气信息")


def text_length_tool(text: str) -> str:
    """计算文本长度的工具"""
    return f"文本长度: {len(text)} 个字符"


def search_tool(query: str) -> str:
    """模拟搜索工具"""
    search_results = {
        "python": "Python是一种高级编程语言，由Guido van Rossum于1991年首次发布。",
        "langchain": "LangChain是一个用于构建基于大语言模型应用程序的框架。",
        "ai": "人工智能(AI)是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。",
        "机器学习": "机器学习是人工智能的一个子集，使计算机能够在没有明确编程的情况下学习和改进。"
    }

    for key, value in search_results.items():
        if key.lower() in query.lower():
            return f"搜索结果: {value}"

    return f"抱歉，没有找到关于'{query}'的相关信息。"


class SimpleAgent:
    """简单的Agent实现，不依赖外部LLM API"""

    def __init__(self):
        self.tools = {
            "calculator": calculator_tool,
            "weather": weather_tool,
            "text_length": text_length_tool,
            "search": search_tool
        }

        self.tool_descriptions = {
            "calculator": "计算数学表达式，例如: 2 + 3 * 4",
            "weather": "获取城市天气信息，例如: 北京",
            "text_length": "计算文本长度，例如: hello world",
            "search": "搜索信息，例如: python"
        }

    def parse_user_input(self, user_input: str) -> Dict[str, Any]:
        """解析用户输入，确定使用哪个工具"""
        user_input_lower = user_input.lower()

        # 数学计算关键词
        math_keywords = ["计算", "算", "+", "-", "*", "/", "sqrt", "sin", "cos", "tan", "log"]
        if any(keyword in user_input_lower for keyword in math_keywords):
            # 提取数学表达式
            expression = user_input
            for word in ["计算", "算一下", "等于多少", "结果是"]:
                expression = expression.replace(word, "").strip()
            return {"tool": "calculator", "input": expression}

        # 天气查询关键词
        weather_keywords = ["天气", "温度", "气温"]
        if any(keyword in user_input for keyword in weather_keywords):
            # 提取城市名
            cities = ["北京", "上海", "广州", "深圳"]
            for city in cities:
                if city in user_input:
                    return {"tool": "weather", "input": city}
            return {"tool": "weather", "input": "北京"}  # 默认城市

        # 文本长度关键词
        length_keywords = ["长度", "字符数", "多少字"]
        if any(keyword in user_input for keyword in length_keywords):
            # 提取要计算长度的文本
            text = user_input
            for word in ["计算", "长度", "字符数", "多少字", "的"]:
                text = text.replace(word, "").strip()
            # 如果提取的文本为空或太短，使用原始输入
            if len(text) < 2:
                text = user_input
            return {"tool": "text_length", "input": text}

        # 默认使用搜索工具
        return {"tool": "search", "input": user_input}

    def execute(self, user_input: str) -> str:
        """执行用户请求"""
        try:
            parsed = self.parse_user_input(user_input)
            tool_name = parsed["tool"]
            tool_input = parsed["input"]

            if tool_name in self.tools:
                result = self.tools[tool_name](tool_input)
                return f"🔧 使用工具: {tool_name}\n📝 输入: {tool_input}\n✅ 结果: {result}"
            else:
                return f"❌ 未找到合适的工具来处理: {user_input}"

        except Exception as e:
            return f"❌ 处理请求时发生错误: {str(e)}"

    def get_help(self) -> str:
        """获取帮助信息"""
        help_text = "🤖 可用功能:\n\n"
        for tool, description in self.tool_descriptions.items():
            help_text += f"• {tool}: {description}\n"

        help_text += "\n💡 示例:\n"
        help_text += "• 计算 2 + 3 * 4\n"
        help_text += "• 北京的天气怎么样\n"
        help_text += "• 计算 hello world 的长度\n"
        help_text += "• 搜索 python\n"

        return help_text


def main():
    print("🤖 LangChain Agent Demo")
    print("=" * 50)

    # 创建简单代理
    agent = SimpleAgent()

    print("\n" + agent.get_help())
    print("\n输入 'help' 查看帮助，'quit' 或 'exit' 退出")
    print("\n开始对话:")

    while True:
        try:
            user_input = input("\n用户: ").strip()

            if user_input.lower() in ['quit', 'exit', '退出']:
                print("再见!")
                break

            if user_input.lower() in ['help', '帮助']:
                print("\n" + agent.get_help())
                continue

            if not user_input:
                continue

            print(f"\n🤔 正在处理: {user_input}")
            print("-" * 50)

            # 执行代理
            response = agent.execute(user_input)

            print(f"\n{response}")

        except KeyboardInterrupt:
            print("\n\n程序被用户中断")
            break
        except Exception as e:
            print(f"\n❌ 发生错误: {str(e)}")


if __name__ == "__main__":
    main()
