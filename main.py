from langchain.agents import initialize_agent, Tool
from langchain.llms import OpenAI
from langchain.tools import DuckDuckGoSearchRun
from langchain.utilities import ArithmeticEvaluator
import os

def main():
    # 设置 OpenAI API key
    os.environ["OPENAI_API_KEY"] = "your-api-key-here"

    # 初始化 LLM
    llm = OpenAI(temperature=0)

    # 创建工具列表
    search = DuckDuckGoSearchRun()
    calculator = ArithmeticEvaluator()

    tools = [
        Tool(
            name="Search",
            func=search.run,
            description="useful for when you need to search for information on the internet"
        ),
        Tool(
            name="Calculator",
            func=calculator.run,
            description="useful for when you need to perform mathematical calculations"
        )
    ]

    # 初始化 agent
    agent = initialize_agent(
        tools, 
        llm, 
        agent="zero-shot-react-description",
        verbose=True
    )

    # 测试 agent
    try:
        # 尝试一个需要计算的问题
        calc_result = agent.run("What is (123 * 456) / 789?")
        print("Calculation result:", calc_result)

        # 尝试一个需要搜索的问题
        search_result = agent.run("What is the capital of France and what is its population?")
        print("Search result:", search_result)
    except Exception as e:
        print(f"An error occurred: {e}")

if __name__ == "__main__":
    main()