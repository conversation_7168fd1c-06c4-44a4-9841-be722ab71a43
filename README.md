# LangChain Agent Demo

这是一个使用LangChain框架构建的智能代理演示项目。该代理可以执行多种任务，包括数学计算、天气查询、文本分析和信息搜索。

## 功能特性

### 🧮 数学计算器
- 支持基本的数学运算（+、-、*、/）
- 支持数学函数（sqrt、sin、cos、tan、log等）
- 安全的表达式计算，防止恶意代码执行

### 🌤️ 天气查询
- 模拟天气信息查询
- 支持多个城市（北京、上海、广州、深圳）
- 可扩展为真实的天气API接口

### 📝 文本分析
- 计算文本长度
- 字符统计功能

### 🔍 信息搜索
- 模拟搜索功能
- 内置知识库查询
- 支持Python、AI、机器学习等主题

## 安装和运行

### 环境要求
- Python 3.12+
- uv 包管理器

### 安装依赖
```bash
# 使用uv安装依赖
uv sync

# 或者手动添加依赖
uv add langchain langchain-openai langchain-core requests
```

### 运行程序
```bash
uv run python main.py
```

## 使用示例

启动程序后，你可以尝试以下命令：

### 数学计算
```
用户: 计算 2 + 3 * 4
代理: 🔧 使用工具: calculator
     📝 输入: 2 + 3 * 4
     ✅ 结果: 计算结果: 14
```

### 天气查询
```
用户: 北京的天气怎么样
代理: 🔧 使用工具: weather
     📝 输入: 北京
     ✅ 结果: 晴天，温度 15°C
```

### 文本长度
```
用户: 计算 hello world 的长度
代理: 🔧 使用工具: text_length
     📝 输入: hello world
     ✅ 结果: 文本长度: 11 个字符
```

### 信息搜索
```
用户: 搜索 python
代理: 🔧 使用工具: search
     📝 输入: python
     ✅ 结果: 搜索结果: Python是一种高级编程语言...
```

## 项目结构

```
agents/
├── main.py          # 主程序文件
├── pyproject.toml   # 项目配置和依赖
├── uv.lock         # 依赖锁定文件
└── README.md       # 项目说明文档
```

## 核心组件

### SimpleAgent 类
- `parse_user_input()`: 解析用户输入，确定使用哪个工具
- `execute()`: 执行用户请求
- `get_help()`: 获取帮助信息

### 工具函数
- `calculator_tool()`: 数学计算工具
- `weather_tool()`: 天气查询工具
- `text_length_tool()`: 文本长度计算工具
- `search_tool()`: 信息搜索工具

## 扩展功能

这个demo可以很容易地扩展更多功能：

1. **真实API集成**: 将模拟的天气和搜索功能替换为真实的API调用
2. **更多工具**: 添加文件操作、网络请求、数据库查询等工具
3. **LLM集成**: 集成OpenAI GPT或其他大语言模型来提供更智能的对话
4. **持久化**: 添加对话历史记录和用户偏好存储
5. **Web界面**: 创建Web界面替代命令行交互

## 技术栈

- **LangChain**: 用于构建AI应用的框架
- **Python**: 主要编程语言
- **uv**: 现代Python包管理器
- **typing**: 类型注解支持

## 注意事项

- 当前版本不需要OpenAI API密钥，使用本地逻辑处理
- 天气和搜索功能使用模拟数据
- 数学计算使用Python的eval函数，已做安全处理

## 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 许可证

MIT License